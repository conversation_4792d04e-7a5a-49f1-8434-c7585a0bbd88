import React, { useState } from 'react';
import { 
  Layout, 
  Card, 
  Button, 
  Table, 
  Space, 
  Typography, 
  Tag, 
  Popconfirm,
  message,
  Row,
  Col
} from 'antd';
import { 
  PlusOutlined, 
  EditOutlined, 
  DeleteOutlined, 
  EnvironmentOutlined,
  BankOutlined 
} from '@ant-design/icons';
import EntityForm from './EntityForm';
import './EntityManagement.scss';

const { Header, Content } = Layout;
const { Title, Text } = Typography;

const EntityManagement = () => {
  const [entities, setEntities] = useState([
    {
      id: 1,
      name: 'TechCorp Solutions',
      code: 'TC001',
      type: 'Headquarters',
      addresses: [
        {
          id: 1,
          type: 'Main Office',
          street: '123 Business Avenue',
          city: 'New York',
          state: 'New York',
          country: 'United States',
          zipCode: '10001',
          phone: '******-0123'
        },
        {
          id: 2,
          type: 'Warehouse',
          street: '456 Industrial Blvd',
          city: 'Newark',
          state: 'New Jersey', 
          country: 'United States',
          zipCode: '07102',
          phone: '******-0124'
        }
      ]
    }
  ]);
  
  const [isModalVisible, setIsModalVisible] = useState(false);
  const [editingEntity, setEditingEntity] = useState(null);

  const handleAddEntity = () => {
    setEditingEntity(null);
    setIsModalVisible(true);
  };

  const handleEditEntity = (entity) => {
    setEditingEntity(entity);
    setIsModalVisible(true);
  };

  const handleDeleteEntity = (entityId) => {
    setEntities(entities.filter(entity => entity.id !== entityId));
    message.success('Entity deleted successfully');
  };

  const handleSaveEntity = (entityData) => {
    if (editingEntity) {
      setEntities(entities.map(entity => 
        entity.id === editingEntity.id 
          ? { ...entityData, id: editingEntity.id }
          : entity
      ));
      message.success('Entity updated successfully');
    } else {
      const newEntity = {
        ...entityData,
        id: Date.now()
      };
      setEntities([...entities, newEntity]);
      message.success('Entity created successfully');
    }
    setIsModalVisible(false);
    setEditingEntity(null);
  };

  const columns = [
    {
      title: 'Entity Details',
      key: 'entity',
      render: (_, record) => (
        <div className="entity-info">
          <div className="entity-header">
            <BankOutlined className="entity-icon" />
            <div>
              <Title level={5} className="entity-name">{record.name}</Title>
              <Text type="secondary">Code: {record.code}</Text>
            </div>
          </div>
          <Tag color="blue" className="entity-type">{record.type}</Tag>
        </div>
      ),
    },
    {
      title: 'Addresses',
      key: 'addresses',
      render: (_, record) => (
        <div className="addresses-list">
          {record.addresses.map((address, index) => (
            <div key={address.id} className="address-item">
              <div className="address-header">
                <EnvironmentOutlined className="address-icon" />
                <Text strong>{address.type}</Text>
              </div>
              <Text className="address-text">
                {address.street}, {address.city}, {address.state}
              </Text>
              <Text type="secondary" className="address-country">
                {address.country} - {address.zipCode}
              </Text>
              {address.phone && (
                <Text type="secondary" className="address-phone">
                  {address.phone}
                </Text>
              )}
            </div>
          ))}
        </div>
      ),
    },
    {
      title: 'Actions',
      key: 'actions',
      width: 120,
      render: (_, record) => (
        <Space>
          <Button 
            type="text" 
            icon={<EditOutlined />} 
            onClick={() => handleEditEntity(record)}
            className="action-btn edit-btn"
          />
          <Popconfirm
            title="Delete Entity"
            description="Are you sure you want to delete this entity?"
            onConfirm={() => handleDeleteEntity(record.id)}
            okText="Yes"
            cancelText="No"
          >
            <Button 
              type="text" 
              icon={<DeleteOutlined />} 
              className="action-btn delete-btn"
            />
          </Popconfirm>
        </Space>
      ),
    },
  ];

  return (
    <Layout className="entity-management">
      <Header className="header">
        <div className="header-content">
          <div className="header-left">
            <BankOutlined className="header-icon" />
            <Title level={3} className="header-title">Entity Management</Title>
          </div>
          <Button 
            type="primary" 
            icon={<PlusOutlined />} 
            onClick={handleAddEntity}
            className="add-btn"
          >
            Add Entity
          </Button>
        </div>
      </Header>
      
      <Content className="content">
        <div className="content-container">
          <Row gutter={[0, 24]}>
            <Col span={24}>
              <Card className="entities-card">
                <div className="card-header">
                  <Title level={4}>Entities & Branches</Title>
                  <Text type="secondary">
                    Manage your organization entities and their multiple addresses
                  </Text>
                </div>
                
                <Table
                  columns={columns}
                  dataSource={entities}
                  rowKey="id"
                  pagination={{
                    pageSize: 10,
                    showSizeChanger: true,
                    showQuickJumper: true,
                    showTotal: (total, range) => 
                      `${range[0]}-${range[1]} of ${total} entities`,
                  }}
                  className="entities-table"
                />
              </Card>
            </Col>
          </Row>
        </div>
      </Content>

      <EntityForm
        visible={isModalVisible}
        entity={editingEntity}
        onSave={handleSaveEntity}
        onCancel={() => {
          setIsModalVisible(false);
          setEditingEntity(null);
        }}
      />
    </Layout>
  );
};

export default EntityManagement;