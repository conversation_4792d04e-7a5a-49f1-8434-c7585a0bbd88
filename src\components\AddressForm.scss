.address-modal {
  .modal-header {
    display: flex;
    align-items: center;
    gap: 8px;

    .modal-icon {
      color: #4a5568;
    }
  }

  .modal-content {
    .address-form {
      .ant-form-item-label > label {
        font-weight: 500;
        color: #2d3748;
      }

      .ant-input,
      .ant-select-selector {
        border-radius: 6px;
        border-color: #d1d5db;

        &:hover {
          border-color: #9ca3af;
        }

        &:focus,
        &.ant-select-focused .ant-select-selector {
          border-color: #3182ce;
          box-shadow: 0 0 0 2px rgba(49, 130, 206, 0.1);
        }
      }
    }

    .modal-footer {
      margin-top: 24px;
      padding-top: 16px;
      border-top: 1px solid #e2e8f0;
      display: flex;
      justify-content: flex-end;
    }
  }
}

// Responsive design
@media (max-width: 768px) {
  .address-modal {
    .ant-modal {
      width: 95% !important;
      margin: 16px auto;
    }

    .modal-content {
      .modal-footer {
        .ant-space {
          width: 100%;
          justify-content: space-between;
        }
      }
    }
  }
}

@media (max-width: 576px) {
  .address-modal {
    .modal-content {
      .address-form {
        .ant-row {
          .ant-col {
            margin-bottom: 8px;
          }
        }
      }
    }
  }
}