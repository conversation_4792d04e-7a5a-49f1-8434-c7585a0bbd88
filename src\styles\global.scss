// Global SCSS variables
$primary-color: #2c3e50;
$secondary-color: #3498db;
$success-color: #27ae60;
$warning-color: #f39c12;
$danger-color: #e74c3c;
$gray-50: #f9fafb;
$gray-100: #f3f4f6;
$gray-200: #e5e7eb;
$gray-300: #d1d5db;
$gray-400: #9ca3af;
$gray-500: #6b7280;
$gray-600: #4b5563;
$gray-700: #374151;
$gray-800: #1f2937;
$gray-900: #111827;

// Global styles
* {
  box-sizing: border-box;
}

body {
  margin: 0;
  padding: 0;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen',
    'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
    sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  background-color: $gray-50;
  color: $gray-900;
}

.app {
  min-height: 100vh;
  background-color: $gray-50;
}

// Ant Design customizations
.ant-layout {
  background-color: $gray-50;
}

.ant-card {
  border-radius: 8px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.12), 0 1px 2px rgba(0, 0, 0, 0.24);
  border: 1px solid $gray-200;
}

.ant-btn {
  border-radius: 6px;
  font-weight: 500;
  transition: all 0.2s ease-in-out;

  &.ant-btn-primary {
    background-color: $primary-color;
    border-color: $primary-color;

    &:hover {
      background-color: lighten($primary-color, 5%);
      border-color: lighten($primary-color, 5%);
    }
  }

  &.ant-btn-dashed {
    &:hover {
      border-color: $secondary-color;
      color: $secondary-color;
    }
  }
}

.ant-table {
  .ant-table-thead > tr > th {
    background-color: $gray-50;
    border-bottom: 1px solid $gray-200;
    font-weight: 600;
    color: $gray-700;
  }

  .ant-table-tbody > tr {
    &:hover > td {
      background-color: $gray-50;
    }
  }

  .ant-table-tbody > tr > td {
    border-bottom: 1px solid $gray-100;
  }
}

.ant-modal {
  .ant-modal-header {
    border-bottom: 1px solid $gray-200;
    border-radius: 8px 8px 0 0;

    .ant-modal-title {
      color: $gray-800;
      font-weight: 600;
    }
  }

  .ant-modal-content {
    border-radius: 8px;
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
  }
}

.ant-form {
  .ant-form-item-label > label {
    color: $gray-700;
    font-weight: 500;
  }

  .ant-input,
  .ant-select-selector {
    border-radius: 6px;
    border-color: $gray-300;
    
    &:hover {
      border-color: $gray-400;
    }

    &:focus,
    &.ant-select-focused .ant-select-selector {
      border-color: $primary-color;
      box-shadow: 0 0 0 2px rgba(44, 62, 80, 0.1);
    }
  }
}

.ant-tag {
  border-radius: 4px;
  font-weight: 500;
  border: none;
}

// Scrollbar styling
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: $gray-100;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb {
  background: $gray-300;
  border-radius: 4px;
  
  &:hover {
    background: $gray-400;
  }
}

// Responsive utilities
.responsive-hide-mobile {
  @media (max-width: 768px) {
    display: none !important;
  }
}

.responsive-hide-desktop {
  @media (min-width: 769px) {
    display: none !important;
  }
}

// Animation utilities
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.fade-in {
  animation: fadeIn 0.3s ease-out;
}

// Focus states for accessibility
.ant-btn:focus,
.ant-input:focus,
.ant-select:focus {
  outline: 2px solid $primary-color;
  outline-offset: 2px;
}