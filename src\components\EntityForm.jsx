import React, { useState, useEffect } from 'react';
import {
  Modal,
  Form,
  Input,
  Select,
  Button,
  Space,
  Typography,
  Divider,
  Card,
  Row,
  Col,
  message
} from 'antd';
import { PlusOutlined, DeleteOutlined, EnvironmentOutlined } from '@ant-design/icons';
import AddressForm from './AddressForm';
import './EntityForm.scss';

const { Title, Text } = Typography;
const { Option } = Select;

const EntityForm = ({ visible, entity, onSave, onCancel }) => {
  const [form] = Form.useForm();
  const [addresses, setAddresses] = useState([]);
  const [editingAddress, setEditingAddress] = useState(null);
  const [addressModalVisible, setAddressModalVisible] = useState(false);

  useEffect(() => {
    if (entity) {
      form.setFieldsValue({
        name: entity.name,
        code: entity.code,
        type: entity.type,
      });
      setAddresses(entity.addresses || []);
    } else {
      form.resetFields();
      setAddresses([]);
    }
  }, [entity, form, visible]);

  const handleSubmit = async () => {
    try {
      const values = await form.validateFields();
      
      if (addresses.length === 0) {
        message.error('Please add at least one address');
        return;
      }

      onSave({
        ...values,
        addresses: addresses
      });
    } catch (error) {
      console.error('Validation failed:', error);
    }
  };

  const handleAddAddress = () => {
    setEditingAddress(null);
    setAddressModalVisible(true);
  };

  const handleEditAddress = (address) => {
    setEditingAddress(address);
    setAddressModalVisible(true);
  };

  const handleDeleteAddress = (addressId) => {
    setAddresses(addresses.filter(addr => addr.id !== addressId));
    message.success('Address removed');
  };

  const handleSaveAddress = (addressData) => {
    if (editingAddress) {
      setAddresses(addresses.map(addr => 
        addr.id === editingAddress.id 
          ? { ...addressData, id: editingAddress.id }
          : addr
      ));
    } else {
      const newAddress = {
        ...addressData,
        id: Date.now()
      };
      setAddresses([...addresses, newAddress]);
    }
    setAddressModalVisible(false);
    setEditingAddress(null);
  };

  return (
    <>
      <Modal
        title={
          <div className="modal-header">
            <EnvironmentOutlined className="modal-icon" />
            <span>{entity ? 'Edit Entity' : 'Create New Entity'}</span>
          </div>
        }
        open={visible}
        onCancel={onCancel}
        footer={null}
        width={800}
        className="entity-modal"
      >
        <div className="modal-content">
          <Form
            form={form}
            layout="vertical"
            className="entity-form"
          >
            <Row gutter={16}>
              <Col span={12}>
                <Form.Item
                  label="Entity Name"
                  name="name"
                  rules={[{ required: true, message: 'Please enter entity name' }]}
                >
                  <Input placeholder="Enter entity name" />
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item
                  label="Entity Code"
                  name="code"
                  rules={[{ required: true, message: 'Please enter entity code' }]}
                >
                  <Input placeholder="Enter entity code" />
                </Form.Item>
              </Col>
            </Row>

            <Row gutter={16}>
              <Col span={24}>
                <Form.Item
                  label="Entity Type"
                  name="type"
                  rules={[{ required: true, message: 'Please select entity type' }]}
                >
                  <Select placeholder="Select entity type">
                    <Option value="Headquarters">Headquarters</Option>
                    <Option value="Branch">Branch</Option>
                    <Option value="Warehouse">Warehouse</Option>
                    <Option value="Office">Office</Option>
                    <Option value="Store">Store</Option>
                  </Select>
                </Form.Item>
              </Col>
            </Row>
          </Form>

          <Divider />

          <div className="addresses-section">
            <div className="section-header">
              <Title level={5}>Addresses</Title>
              <Button 
                type="dashed" 
                icon={<PlusOutlined />} 
                onClick={handleAddAddress}
                className="add-address-btn"
              >
                Add Address
              </Button>
            </div>

            {addresses.length === 0 ? (
              <div className="empty-addresses">
                <Text type="secondary">No addresses added yet. Click "Add Address" to get started.</Text>
              </div>
            ) : (
              <div className="addresses-list">
                {addresses.map((address) => (
                  <Card 
                    key={address.id} 
                    size="small" 
                    className="address-card"
                    actions={[
                      <Button 
                        type="text" 
                        size="small" 
                        onClick={() => handleEditAddress(address)}
                      >
                        Edit
                      </Button>,
                      <Button 
                        type="text" 
                        size="small" 
                        danger 
                        onClick={() => handleDeleteAddress(address.id)}
                      >
                        Delete
                      </Button>
                    ]}
                  >
                    <div className="address-content">
                      <div className="address-type">
                        <Text strong>{address.type}</Text>
                      </div>
                      <div className="address-details">
                        <Text>{address.street}</Text>
                        <br />
                        <Text>{address.city}, {address.state}</Text>
                        <br />
                        <Text type="secondary">{address.country} - {address.zipCode}</Text>
                        {address.phone && (
                          <>
                            <br />
                            <Text type="secondary">{address.phone}</Text>
                          </>
                        )}
                      </div>
                    </div>
                  </Card>
                ))}
              </div>
            )}
          </div>

          <div className="modal-footer">
            <Space>
              <Button onClick={onCancel}>Cancel</Button>
              <Button type="primary" onClick={handleSubmit}>
                {entity ? 'Update Entity' : 'Create Entity'}
              </Button>
            </Space>
          </div>
        </div>
      </Modal>

      <AddressForm
        visible={addressModalVisible}
        address={editingAddress}
        onSave={handleSaveAddress}
        onCancel={() => {
          setAddressModalVisible(false);
          setEditingAddress(null);
        }}
      />
    </>
  );
};

export default EntityForm;