import React, { useEffect } from 'react';
import {
  Modal,
  Form,
  Input,
  Select,
  Row,
  Col,
  Button,
  Space
} from 'antd';
import { EnvironmentOutlined } from '@ant-design/icons';
import './AddressForm.scss';

const { Option } = Select;

const countries = [
  'United States', 'Canada', 'United Kingdom', 'Germany', 'France', 
  'Japan', 'Australia', 'India', 'China', 'Brazil'
];

const usStates = [
  'Alabama', 'Alaska', 'Arizona', 'Arkansas', 'California', 'Colorado',
  'Connecticut', 'Delaware', 'Florida', 'Georgia', 'Hawaii', 'Idaho',
  'Illinois', 'Indiana', 'Iowa', 'Kansas', 'Kentucky', 'Louisiana',
  'Maine', 'Maryland', 'Massachusetts', 'Michigan', 'Minnesota',
  'Mississippi', 'Missouri', 'Montana', 'Nebraska', 'Nevada',
  'New Hampshire', 'New Jersey', 'New Mexico', 'New York',
  'North Carolina', 'North Dakota', 'Ohio', 'Oklahoma', 'Oregon',
  'Pennsylvania', 'Rhode Island', 'South Carolina', 'South Dakota',
  'Tennessee', 'Texas', 'Utah', 'Vermont', 'Virginia', 'Washington',
  'West Virginia', 'Wisconsin', 'Wyoming'
];

const addressTypes = [
  'Main Office', 'Branch Office', 'Headquarters', 'Warehouse', 
  'Distribution Center', 'Store', 'Manufacturing Plant', 'Service Center'
];

const AddressForm = ({ visible, address, onSave, onCancel }) => {
  const [form] = Form.useForm();

  useEffect(() => {
    if (address) {
      form.setFieldsValue(address);
    } else {
      form.resetFields();
    }
  }, [address, form, visible]);

  const handleSubmit = async () => {
    try {
      const values = await form.validateFields();
      onSave(values);
    } catch (error) {
      console.error('Validation failed:', error);
    }
  };

  return (
    <Modal
      title={
        <div className="modal-header">
          <EnvironmentOutlined className="modal-icon" />
          <span>{address ? 'Edit Address' : 'Add New Address'}</span>
        </div>
      }
      open={visible}
      onCancel={onCancel}
      footer={null}
      width={600}
      className="address-modal"
    >
      <div className="modal-content">
        <Form
          form={form}
          layout="vertical"
          className="address-form"
        >
          <Row gutter={16}>
            <Col span={24}>
              <Form.Item
                label="Address Type"
                name="type"
                rules={[{ required: true, message: 'Please select address type' }]}
              >
                <Select placeholder="Select address type">
                  {addressTypes.map(type => (
                    <Option key={type} value={type}>{type}</Option>
                  ))}
                </Select>
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={16}>
            <Col span={24}>
              <Form.Item
                label="Street Address"
                name="street"
                rules={[{ required: true, message: 'Please enter street address' }]}
              >
                <Input placeholder="Enter street address" />
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                label="City"
                name="city"
                rules={[{ required: true, message: 'Please enter city' }]}
              >
                <Input placeholder="Enter city" />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                label="State/Province"
                name="state"
                rules={[{ required: true, message: 'Please select state' }]}
              >
                <Select 
                  placeholder="Select state"
                  showSearch
                  filterOption={(input, option) =>
                    option.children.toLowerCase().indexOf(input.toLowerCase()) >= 0
                  }
                >
                  {usStates.map(state => (
                    <Option key={state} value={state}>{state}</Option>
                  ))}
                </Select>
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                label="Country"
                name="country"
                rules={[{ required: true, message: 'Please select country' }]}
              >
                <Select placeholder="Select country">
                  {countries.map(country => (
                    <Option key={country} value={country}>{country}</Option>
                  ))}
                </Select>
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                label="ZIP/Postal Code"
                name="zipCode"
                rules={[{ required: true, message: 'Please enter ZIP code' }]}
              >
                <Input placeholder="Enter ZIP code" />
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={16}>
            <Col span={24}>
              <Form.Item
                label="Phone Number"
                name="phone"
              >
                <Input placeholder="Enter phone number (optional)" />
              </Form.Item>
            </Col>
          </Row>
        </Form>

        <div className="modal-footer">
          <Space>
            <Button onClick={onCancel}>Cancel</Button>
            <Button type="primary" onClick={handleSubmit}>
              {address ? 'Update Address' : 'Add Address'}
            </Button>
          </Space>
        </div>
      </div>
    </Modal>
  );
};

export default AddressForm;