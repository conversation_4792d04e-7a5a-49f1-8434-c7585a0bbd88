import React from 'react';
import { ConfigProvider } from 'antd';
import EntityManagement from './components/EntityManagement';
import './styles/global.scss';

const theme = {
  token: {
    colorPrimary: '#2c3e50',
    colorSuccess: '#27ae60',
    colorWarning: '#f39c12',
    colorError: '#e74c3c',
    colorInfo: '#3498db',
    borderRadius: 6,
    fontFamily: '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif',
  },
};

function App() {
  return (
    <ConfigProvider theme={theme}>
      <div className="app">
        <EntityManagement />
      </div>
    </ConfigProvider>
  );
}

export default App;