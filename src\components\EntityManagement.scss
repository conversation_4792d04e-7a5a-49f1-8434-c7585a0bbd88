.entity-management {
  min-height: 100vh;
  background-color: #f8fafc;

  .header {
    background: #ffffff;
    padding: 0;
    border-bottom: 1px solid #e2e8f0;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);

    .header-content {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 0 24px;
      max-width: 1200px;
      margin: 0 auto;

      .header-left {
        display: flex;
        align-items: center;
        gap: 12px;

        .header-icon {
          font-size: 24px;
          color: #2c3e50;
        }

        .header-title {
          margin: 0;
          color: #1a202c;
          font-weight: 600;
        }
      }

      .add-btn {
        border-radius: 6px;
        font-weight: 500;
      }
    }
  }

  .content {
    padding: 24px;

    .content-container {
      max-width: 1200px;
      margin: 0 auto;
    }
  }

  .entities-card {
    border-radius: 8px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    border: 1px solid #e2e8f0;

    .card-header {
      margin-bottom: 24px;

      h4 {
        margin-bottom: 4px;
        color: #1a202c;
        font-weight: 600;
      }
    }
  }

  .entities-table {
    .entity-info {
      .entity-header {
        display: flex;
        align-items: center;
        gap: 12px;
        margin-bottom: 8px;

        .entity-icon {
          font-size: 20px;
          color: #4a5568;
        }

        .entity-name {
          margin: 0;
          color: #1a202c;
        }
      }

      .entity-type {
        border-radius: 4px;
      }
    }

    .addresses-list {
      .address-item {
        margin-bottom: 16px;
        padding: 12px;
        background: #f7fafc;
        border-radius: 6px;
        border-left: 3px solid #e2e8f0;

        &:last-child {
          margin-bottom: 0;
        }

        .address-header {
          display: flex;
          align-items: center;
          gap: 8px;
          margin-bottom: 4px;

          .address-icon {
            color: #4a5568;
          }
        }

        .address-text,
        .address-country,
        .address-phone {
          display: block;
          margin-bottom: 2px;
        }
      }
    }

    .action-btn {
      border-radius: 4px;

      &.edit-btn:hover {
        color: #3182ce;
        background-color: #ebf8ff;
      }

      &.delete-btn:hover {
        color: #e53e3e;
        background-color: #fed7d7;
      }
    }
  }
}

// Responsive design
@media (max-width: 768px) {
  .entity-management {
    .header {
      .header-content {
        padding: 0 16px;
        flex-direction: column;
        gap: 16px;
        padding-top: 16px;
        padding-bottom: 16px;

        .header-left {
          .header-title {
            font-size: 20px;
          }
        }
      }
    }

    .content {
      padding: 16px;
    }

    .entities-table {
      .ant-table-tbody > tr > td {
        padding: 12px 8px;
      }

      .entity-info {
        .entity-header {
          flex-direction: column;
          align-items: flex-start;
          gap: 4px;
        }
      }

      .addresses-list {
        .address-item {
          padding: 8px;
          margin-bottom: 12px;
        }
      }
    }
  }
}

@media (max-width: 576px) {
  .entity-management {
    .entities-table {
      .ant-table-thead > tr > th:last-child,
      .ant-table-tbody > tr > td:last-child {
        width: 80px;
      }
    }
  }
}