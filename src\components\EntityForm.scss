.entity-modal {
  .modal-header {
    display: flex;
    align-items: center;
    gap: 8px;

    .modal-icon {
      color: #4a5568;
    }
  }

  .modal-content {
    .entity-form {
      .ant-form-item-label > label {
        font-weight: 500;
        color: #2d3748;
      }
    }

    .addresses-section {
      .section-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 16px;

        h5 {
          margin: 0;
          color: #2d3748;
        }

        .add-address-btn {
          border-color: #cbd5e0;
          color: #4a5568;

          &:hover {
            border-color: #3182ce;
            color: #3182ce;
          }
        }
      }

      .empty-addresses {
        text-align: center;
        padding: 32px;
        background: #f7fafc;
        border-radius: 6px;
        border: 2px dashed #cbd5e0;
      }

      .addresses-list {
        display: grid;
        gap: 12px;
        max-height: 300px;
        overflow-y: auto;

        .address-card {
          border: 1px solid #e2e8f0;
          border-radius: 6px;

          .ant-card-actions {
            background: #f7fafc;
          }

          .address-content {
            .address-type {
              margin-bottom: 8px;
            }

            .address-details {
              line-height: 1.4;
            }
          }
        }
      }
    }

    .modal-footer {
      margin-top: 24px;
      padding-top: 16px;
      border-top: 1px solid #e2e8f0;
      display: flex;
      justify-content: flex-end;
    }
  }
}

// Responsive design
@media (max-width: 768px) {
  .entity-modal {
    .ant-modal {
      width: 95% !important;
      margin: 16px auto;
    }

    .modal-content {
      .addresses-section {
        .section-header {
          flex-direction: column;
          align-items: flex-start;
          gap: 12px;
        }

        .addresses-list {
          .address-card {
            .ant-card-actions {
              li {
                margin: 4px 0;
              }
            }
          }
        }
      }

      .modal-footer {
        .ant-space {
          width: 100%;
          justify-content: space-between;
        }
      }
    }
  }
}